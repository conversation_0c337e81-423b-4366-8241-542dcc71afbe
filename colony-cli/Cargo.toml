[package]
name = "colony-cli"
version = "0.1.1"
edition = "2024"
authors = ["<PERSON> M<PERSON>lish"]
description = "A colonylib CLI for interacting with the colony-daemon"
homepage = "https://github.com/zettawatt/colony-utils"
license = "GPL-3.0-only"

[dependencies]
clap = "4.5.39"
indicatif = "0.17.11"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.12", features = ["json"] }
colored = "2.0"
anyhow = "1.0"
uuid = { version = "1.0", features = ["v4"] }
dialoguer = "0.11.0"
dirs = "6.0.0"
